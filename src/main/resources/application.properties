spring.application.name=AI Commons

logging.level.com.enttribe.commons.ai=DEBUG

#prompt.service.url=http://localhost:8081/prompt-analyzer/rest
#prompt.service.url=https://dev.visionwaves.com/promptsmith

commons.ai.sdk.is_local=true
commons.ai.sdk.app.name=EMAIL_ASSISTANT_APP_NAME
commons.ai.sdk.default.llm.model=llama-3.3-70b-versatile
commons.ai.sdk.prompt.refresh.interval=100000000


#commons.ai.sdk.vector_store.config=[{"ssl":{"enabled":"true"},"vectorDatabase":"redis","vector_store_id":"1","inference":"huggingface","embeddingModel":"BAAI/bge-base-en-v1.5","host":"localhost","port":6379,"username":"","password":"","initializeSchema":true,"indexName":"spring","prefix":"abc"}]
commons.ai.sdk.vector_store.config=W3sic3NsIjp7ImVuYWJsZWQiOiJmYWxzZSJ9LCJ2ZWN0b3JEYXRhYmFzZSI6InJlZGlzIiwidmVjdG9yX3N0b3JlX2lkIjoiYWJjIiwiaW5mZXJlbmNlIjoiaHVnZ2luZ2ZhY2UiLCJlbWJlZGRpbmdNb2RlbCI6IkJBQUkvYmdlLWJhc2UtZW4tdjEuNSIsInNlbnRpbmVsIjp7Im1hc3RlciI6Im15bWFzdGVyIiwibm9kZXMiOlsicmVkaXMtbm9kZS0wLnJlZGlzLWhlYWRsZXNzLnZlY3Rvci1yZWRpcy5zdmMuY2x1c3Rlci5sb2NhbDoyNjM3OSIsInJlZGlzLW5vZGUtMS5yZWRpcy1oZWFkbGVzcy52ZWN0b3ItcmVkaXMuc3ZjLmNsdXN0ZXIubG9jYWw6MjYzNzkiLCJyZWRpcy1ub2RlLTIucmVkaXMtaGVhZGxlc3MudmVjdG9yLXJlZGlzLnN2Yy5jbHVzdGVyLmxvY2FsOjI2Mzc5Il19LCJob3N0IjoibG9jYWxob3N0IiwicG9ydCI6NjM3OSwidXNlcm5hbWUiOiIiLCJwYXNzd29yZCI6IiIsImluaXRpYWxpemVTY2hlbWEiOmZhbHNlLCJpbmRleE5hbWUiOiJ2ZWN0b3Jfc3RvcmVfa25vd2xlZGdlX2Jhc2UiLCJwcmVmaXgiOiJwcm9tcHRfc21pdGhfIiwibWV0YWRhdGFGaWVsZHMiOlt7Im5hbWUiOiJkb2NfaWQiLCJmaWVsZFR5cGUiOiJUQUcifSx7Im5hbWUiOiJjdXN0b21fYWdlbnRfaWQiLCJmaWVsZFR5cGUiOiJOVU1FUklDIn1dfV0=


commons.ai.sdk.intent.metadata=

commons.ai.sdk.vector.max.token.per_document=50000
commons.ai.sdk.vector.max.document.per_request=50
commons.ai.sdk.secret-key=secret-key
commons.ai.sdk.chat-size=10

exception.audit.enable=true
prompt.audit.enable=true
tool.audit.enable=true


spring.ai.openai.api-key=********************************************************
spring.ai.openai.chat.base-url=https://api.groq.com/openai/
spring.ai.openai.chat.options.model=llama-3.3-70b-versatile

##standalone,sentinel,standalone_ssl
#commons.ai.sdk.chat.redis.enable=true
#commons.ai.sdk.chat.redis.type=standalone_ssl

##standalone
#commons.ai.sdk.redis.host=localhost
#commons.ai.sdk.redis.port=6378

##standalone_ssl
#commons.ai.sdk.redis.host=localhost
#commons.ai.sdk.redis.port=6378
#commons.ai.sdk.redis.password=9fe9dad0-acb6-4e6b-8643-a350f35f502b
#commons.ai.sdk.chat.redis.trustStorePath=/Users/<USER>/Downloads/redis_gcp.p12
#commons.ai.sdk.chat.redis.trustStorePassword=changeit

##sentinel
#commons.ai.sdk.redis.sentinel.master=mymaster
#commons.ai.sdk.redis.sentinel.nodes=127.0.0.1:26379,127.0.0.1:26380,127.0.0.1:26381
#commons.ai.sdk.redis.password=
