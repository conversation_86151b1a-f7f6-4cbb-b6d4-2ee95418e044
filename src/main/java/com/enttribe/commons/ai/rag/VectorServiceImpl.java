package com.enttribe.commons.ai.rag;

import com.enttribe.commons.ai.advisor.QuestionAnswerAdvisor;
import com.enttribe.commons.ai.config.VectorStoreConfig;
import com.enttribe.commons.ai.manager.InferenceManager;
import com.enttribe.commons.ai.model.rag.VectorMetaData;
import com.enttribe.commons.ai.util.CommonsAISaaSUtil;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.document.Document;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.vectorstore.SearchRequest;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.ai.vectorstore.filter.Filter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Map;

@Service
public class VectorServiceImpl implements VectorService {

    @Value("${commons.ai.sdk.vector.max.token.per_document:50000}")
    private Integer maxTokenPerDocument;

    @Value("${commons.ai.sdk.vector.max.document.per_request:50}")
    private Integer maxDocumentsPerRequest;

    private final InferenceManager inferenceManager;
    private final VectorStoreConfig vectorStoreConfig;
    private final HttpServletRequest httpServletRequest;

    public VectorServiceImpl(InferenceManager inferenceManager, VectorStoreConfig vectorStoreConfig, HttpServletRequest httpServletRequest) {
        this.inferenceManager = inferenceManager;
        this.vectorStoreConfig = vectorStoreConfig;
        this.httpServletRequest = httpServletRequest;
    }

    @Override
    public List<Document> similaritySearch(VectorMetaData vectorMetaData, SearchRequest searchRequest) {
        String customerID = CommonsAISaaSUtil.getCustomerID(httpServletRequest);
        vectorMetaData.setCustomerId(customerID);
        VectorStore vectorStore = vectorStoreConfig.getVectorStore(vectorMetaData);
        return vectorStore.similaritySearch(searchRequest);
    }

    @Override
    public void deleteDocuments(String vectorStoreId, List<String> documentIds) {
        String customerID = CommonsAISaaSUtil.getCustomerID(httpServletRequest);
        VectorStore vectorStore = vectorStoreConfig.getVectorStore(vectorStoreId, customerID);
        vectorStore.delete(documentIds);
    }

    @Override
    public String saveDocument(String vectorStoreId, Document document) {
        validate(document);
        String customerID = CommonsAISaaSUtil.getCustomerID(httpServletRequest);
        VectorStore vectorStore = vectorStoreConfig.getVectorStore(vectorStoreId, customerID);
        vectorStore.accept(List.of(document));
        return document.getId();
    }

    @Override
    public void saveDocuments(String vectorStoreId, List<Document> documents) {
        validate(documents);
        String customerID = CommonsAISaaSUtil.getCustomerID(httpServletRequest);
        VectorStore vectorStore = vectorStoreConfig.getVectorStore(vectorStoreId, customerID);
        vectorStore.accept(documents);
    }

    @Override
    public List<Document> similaritySearch(String vectorStoreId, SearchRequest searchRequest) {
        String customerID = CommonsAISaaSUtil.getCustomerID(httpServletRequest);
        VectorStore vectorStore = vectorStoreConfig.getVectorStore(vectorStoreId, customerID);
        return vectorStore.similaritySearch(searchRequest);
    }

    @Override
    public void deleteDocuments(String vectorStoreId, String filterExpression) {
        String customerID = CommonsAISaaSUtil.getCustomerID(httpServletRequest);
        VectorStore vectorStore = vectorStoreConfig.getVectorStore(vectorStoreId, customerID);
        vectorStore.delete(filterExpression);
    }

    @Override
    public void deleteDocuments(String vectorStoreId, Map<String, Object> filterMap) {
        String customerID = CommonsAISaaSUtil.getCustomerID(httpServletRequest);
        VectorStore vectorStore = vectorStoreConfig.getVectorStore(vectorStoreId, customerID);
        String filterExpression = createFilterExpression(filterMap);
        vectorStore.delete(filterExpression);
    }

    @Override
    public void deleteDocuments(String vectorStoreId, Filter.Expression filterExpression) {
        String customerID = CommonsAISaaSUtil.getCustomerID(httpServletRequest);
        VectorStore vectorStore = vectorStoreConfig.getVectorStore(vectorStoreId, customerID);
        vectorStore.delete(filterExpression);
    }

    @Override
    public String getAnswerFromDocument(VectorMetaData vectorMetaData, String question, SearchRequest searchRequest) {
        String customerID = CommonsAISaaSUtil.getCustomerID(httpServletRequest);
        vectorMetaData.setCustomerId(customerID);
        VectorStore vectorStore = vectorStoreConfig.getVectorStore(vectorMetaData);
        ChatModel chatModel = inferenceManager.getChatModelByProvider(vectorMetaData.getProvider(), vectorMetaData.getCustomerId());

        OpenAiChatOptions chatOptions = OpenAiChatOptions.builder()
                .model(vectorMetaData.getChatModel())
                .build();

        ChatResponse chatResponse = ChatClient.builder(chatModel)
                .build().prompt()
                .user(question)
                .options(chatOptions)
                .advisors(new QuestionAnswerAdvisor(vectorStore, searchRequest))
                .call()
                .chatResponse();

        return chatResponse.getResult().getOutput().getText();
    }

    private void validate(List<Document> documents) {
        Assert.notNull(documents, "document must not be null");
        Assert.state(documents.size() <= maxDocumentsPerRequest,
                String.format("max %s documents are allowed per request. You can modify the limit by updating 'commons.ai.sdk.vector.max.document.per_request' property", maxDocumentsPerRequest)
        );
        for (Document document : documents) {
            Assert.notNull(document.getText(), "document text must not be null");
            if (document.getText().length() > maxTokenPerDocument) {
                throw new UnsupportedOperationException(
                        String.format("single document must not cross %s token limit. You can modify the limit by updating 'commons.ai.sdk.vector.max.token.per_document' property", maxTokenPerDocument)
                );
            }
        }
    }


    private void validate(Document document) {
        Assert.notNull(document, "document must not be null");
        Assert.notNull(document.getText(), "document text must not be null");
        if (document.getText().length() > maxTokenPerDocument) {
            throw new UnsupportedOperationException(
                    String.format("single document must not cross %s token limit. You can modify the limit by updating 'commons.ai.sdk.vector.max.token.per_document' property", maxTokenPerDocument)
            );
        }
    }

    private String createFilterExpression(Map<String, Object> metadata) {
        StringBuilder expression = new StringBuilder();

        for (Map.Entry<String, Object> entry : metadata.entrySet()) {
            if (!expression.isEmpty()) {
                expression.append(" and ");
            }
            String key = entry.getKey();
            Object value = entry.getValue();

            expression.append("\"").append(key).append("\" == ");

            if (value instanceof String) {
                expression.append("\"").append(value).append("\"");
            } else if (value instanceof Number) {
                expression.append(value);
            }
        }
        return expression.toString();
    }

}
