package com.enttribe.commons.ai.rag;

import com.enttribe.commons.ai.model.rag.VectorMetaData;
import org.springframework.ai.document.Document;
import org.springframework.ai.vectorstore.SearchRequest;
import org.springframework.ai.vectorstore.filter.Filter;

import java.util.List;
import java.util.Map;

/**
 * Service interface for managing vector-based document operations and similarity searches.
 * This service provides functionality for storing, retrieving, and searching documents
 * using vector embeddings, as well as performing similarity-based searches and question answering.
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface VectorService {


    /**
     * Performs a similarity search on stored documents based on the provided search request.
     *
     * @param metaData      metadata containing information about the vector store configuration
     * @param searchRequest the search parameters and criteria
     * @return a list of documents that match the similarity search criteria
     */
    List<Document> similaritySearch(VectorMetaData metaData, SearchRequest searchRequest);

    void deleteDocuments(String vectorStoreId, List<String> documentIds);

    String saveDocument(String vectorStoreId, Document document);

    void saveDocuments(String vectorStoreId, List<Document> documents);

    List<Document> similaritySearch(String vectorStoreId, SearchRequest searchRequest);

    void deleteDocuments(String vectorStoreId, String filterExpression);

    void deleteDocuments(String vectorStoreId, Map<String, Object> filterExpression);

    void deleteDocuments(String vectorStoreId, Filter.Expression filterExpression);

    /**
     * Retrieves an answer to a question by searching through relevant documents.
     *
     * @param metaData      metadata containing information about the vector store configuration
     * @param question      the question to be answered
     * @param searchRequest the search parameters and criteria
     * @return the generated answer based on the relevant documents found
     */
    String getAnswerFromDocument(VectorMetaData metaData, String question, SearchRequest searchRequest);

}
