package com.enttribe.commons.ai.knowledge_base;


import com.enttribe.commons.ai.chat.AiChatModel;
import com.enttribe.commons.ai.constant.SdkConstants;
import com.enttribe.commons.ai.model.KnowledgeBase;
import com.enttribe.commons.ai.model.rag.VectorMetaData;
import com.enttribe.commons.ai.rag.VectorService;
import com.enttribe.commons.ai.util.JsonUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.document.Document;
import org.springframework.ai.vectorstore.SearchRequest;

import java.util.List;
import java.util.Map;
import java.util.function.Function;

public class KnowledgeBaseReturnDirect implements Function<KnowledgeBaseReturnDirect.Request, KnowledgeBaseReturnDirect.Response> {

    private static final Logger log = LoggerFactory.getLogger(KnowledgeBaseReturnDirect.class);
    private final VectorService vectorService;
    private final VectorMetaData vectorMetaData;
    private final AiChatModel aiChatModel;
    private final String filterExpression;
    private SearchRequest searchRequest;

    private static final String PROMPT_ID = "CONVERSATION_AI-KnowledgeBaseTool-Knowledge_Base_Tool_prompt_v-1";

    public KnowledgeBaseReturnDirect(VectorService vectorService, KnowledgeBase knowledgeBase,
                                     AiChatModel aiChatModel, String filterExpression) {
        String metaData = knowledgeBase.getVectorMetaData();
        log.info("initializing KnowledgeBaseReturnDirect tool. metaData : {}", metaData);
        this.vectorService = vectorService;
        this.filterExpression = filterExpression;
        this.vectorMetaData = VectorMetaData.fromMap(getMetaDataMap(metaData));
        this.searchRequest = SearchRequest.builder()
                .similarityThreshold(knowledgeBase.getSimilarityThreshold())
                .topK(knowledgeBase.getTopK()).build();
        this.aiChatModel = aiChatModel;
        log.info("KnowledgeBaseReturnDirect tool initialized with filterExpression : {}", filterExpression);
    }

    @Override
    public Response apply(Request request) {
        log.info("inside function KnowledgeBase. request : {}", request.question());
        if (filterExpression != null) {
            searchRequest = SearchRequest.from(searchRequest).query(request.question()).filterExpression(filterExpression).build();
        }
        List<Document> documents = vectorService.similaritySearch(vectorMetaData, searchRequest);
        String context = prepareContext(documents);

        return aiChatModel.chatCompletion(PROMPT_ID, SdkConstants.FIX, Map.of("context", context, "userMessage", request.question()), Response.class);
    }

    public record Request(@JsonProperty("question") String question) {
    }

    public record Response(
            @JsonPropertyDescription("answer of the question") String content,
            @JsonPropertyDescription("metadata of the document used for answering") Map<String, Object> metadata,
            @JsonPropertyDescription("exact line the answer is taken from") String contextLine
    ) { }

    private String prepareContext(List<Document> documents) {
        StringBuilder context = new StringBuilder();
        for (Document document : documents) {
            context.append("content : ").append(document.getText()).append("\n");
            context.append("metadata : ").append(JsonUtils.convertToJSON(document.getMetadata())).append("\n-------------");
        }
        return context.toString();
    }

    private Map<String, String> getMetaDataMap(String metaData) {
        ObjectMapper objectMapper = JsonUtils.getObjectMapper();
        try {
            return objectMapper.readValue(metaData, Map.class);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

}
