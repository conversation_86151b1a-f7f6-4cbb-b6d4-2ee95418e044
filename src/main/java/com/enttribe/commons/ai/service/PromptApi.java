package com.enttribe.commons.ai.service;

import com.enttribe.commons.ai.config.RestTemplateSingleton;
import com.enttribe.commons.ai.constant.SdkConstants;
import com.enttribe.commons.ai.model.InferenceDetail;
import com.enttribe.commons.ai.model.KnowledgeBase;
import com.enttribe.commons.ai.model.PromptModel;
import com.enttribe.commons.ai.model.Tool;
import com.enttribe.commons.ai.util.CommonsAISaaSUtil;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.List;

/**
 * Service class responsible for making HTTP requests to the Prompt Analyzer service.
 * This class handles all API interactions related to prompts, tools, LLM models, and knowledge base operations.
 * It provides methods to retrieve various AI-related configurations and data from the prompt analyzer service.
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class PromptApi {

    private static final Logger log = LoggerFactory.getLogger(PromptApi.class);

//        public static final String LOCAL_URL = "http://localhost:8081/prompt-analyzer/rest";
    public static final String LOCAL_URL = "https://dev.visionwaves.com/promptsmith";

    private String promptServiceUrl = "http://prompt-analyzer-service.ansible.svc.cluster.local/prompt-analyzer/rest";

    @Value("${commons.ai.sdk.is_local:false}")
    private Boolean isLocal;

    private final HttpServletRequest httpServletRequest;

    public PromptApi(HttpServletRequest httpServletRequest) {
        this.httpServletRequest = httpServletRequest;
    }

    /**
     * Retrieves all prompts associated with a specific application.
     *
     * @param applicationName the name of the application to retrieve prompts for
     * @return List of {@link PromptModel} objects, or empty list if none found or in case of error
     */
    public List<PromptModel> getPromptsOfApplication(String applicationName) {
        log.debug("making call to prompt analyzer for loading prompts");

        RestTemplate restTemplate = RestTemplateSingleton.getRestTemplate();
        // Create HTTP headers (you can add more headers like Authorization if needed)
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(org.springframework.http.MediaType.APPLICATION_JSON);

        if (isLocal) promptServiceUrl = LOCAL_URL;
        String url = String.format("%s/prompt/getPromptByApplication/%s", promptServiceUrl, applicationName);
        log.debug("url to get prompts by application : {}", url);

        // Wrap the headers in an HttpEntity for GET request (optional)
        HttpEntity<Void> entity = new HttpEntity<>(headers);

        try {
            // Make the GET request to the audit endpoint
            ResponseEntity<List<PromptModel>> response = restTemplate.exchange(
                    url,
                    HttpMethod.GET,
                    entity,
                    new ParameterizedTypeReference<List<PromptModel>>() {
                    }
            );
            return response.getBody();
        } catch (Exception e) {
            log.error("Error while retrieving prompts for application : {}", applicationName, e);
            return new ArrayList<>();
        }
    }


    /**
     * Retrieves tools by their unique identifiers.
     *
     * @param ids list of tool IDs to retrieve
     * @return List of {@link Tool} objects matching the provided IDs, or empty list if none found or in case of error
     */
    public List<Tool> getToolsByIds(List<String> ids) {
        log.debug("making call to prompt analyzer for loading tools by ids");

        RestTemplate restTemplate = RestTemplateSingleton.getRestTemplate();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(org.springframework.http.MediaType.APPLICATION_JSON);
        headers.set(SdkConstants.ACCESS_TOKEN_HEADER, CommonsAISaaSUtil.getAccessToken(httpServletRequest));

        if (isLocal) promptServiceUrl = LOCAL_URL;
        String url = String.format("%s/tool/v1/getToolsByIds", promptServiceUrl);
        log.debug("url to get tools by ids : {}", url);

        HttpEntity<List<String>> entity = new HttpEntity<>(ids, headers);

        try {
            // Make the POST request
            ResponseEntity<List<Tool>> response = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    entity,
                    new ParameterizedTypeReference<List<Tool>>() {
                    }
            );
            return response.getBody();
        } catch (Exception e) {
            log.error("Error while retrieving tools for ids : {}", ids, e);
            return new ArrayList<>();
        }
    }

    /**
     * Retrieves a specific prompt by its unique identifier.
     *
     * @param id the unique identifier of the prompt
     * @return {@link PromptModel} object matching the ID
     * @throws RuntimeException if the prompt cannot be retrieved
     */
    public PromptModel getPromptById(String id) {
        log.debug("making call to getPromptById");

        RestTemplate restTemplate = RestTemplateSingleton.getRestTemplate();
        // Create HTTP headers (you can add more headers like Authorization if needed)
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(org.springframework.http.MediaType.APPLICATION_JSON);
        headers.set(SdkConstants.ACCESS_TOKEN_HEADER, CommonsAISaaSUtil.getAccessToken(httpServletRequest));

        if (isLocal) promptServiceUrl = LOCAL_URL;
        String url = String.format("%s/prompt/v1/getPromptById/%s", promptServiceUrl, id);
        log.debug("url to get prompt by id : {}", url);

        // Wrap the headers in an HttpEntity for GET request (optional)
        HttpEntity<Void> entity = new HttpEntity<>(headers);

        try {
            // Make the GET request to the audit endpoint
            ResponseEntity<PromptModel> response = restTemplate.exchange(
                    url,
                    HttpMethod.GET,
                    entity,
                    new ParameterizedTypeReference<PromptModel>() {
                    }
            );
            return response.getBody();
        } catch (Exception e) {
            log.error("Error while retrieving prompt for id : {}", id, e);
            throw new RuntimeException(e);
        }
    }

    /**
     * Retrieves all LLM (Language Learning Model) configurations for a specific application.
     *
     * @param applicationName the name of the application to retrieve LLM models for
     * @return List of {@link InferenceDetail} objects, or empty list if none found or in case of error
     */
    public List<InferenceDetail> getLlmModels(String applicationName) {
        log.debug("making call to prompt analyzer for loading llm models");

        RestTemplate restTemplate = RestTemplateSingleton.getRestTemplate();
        // Create HTTP headers (you can add more headers like Authorization if needed)
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(org.springframework.http.MediaType.APPLICATION_JSON);

        if (isLocal) promptServiceUrl = LOCAL_URL;
        String url = String.format("%s/llm-model/getLlmModelsForSDK/%s", promptServiceUrl, applicationName);
        log.debug("url to get llm models for sdk : {}", url);

        // Wrap the headers in an HttpEntity for GET request (optional)
        HttpEntity<Void> entity = new HttpEntity<>(headers);

        try {
            // Make the GET request to the audit endpoint
            ResponseEntity<List<InferenceDetail>> response = restTemplate.exchange(
                    url,
                    HttpMethod.GET,
                    entity,
                    new ParameterizedTypeReference<List<InferenceDetail>>() {
                    }
            );
            return response.getBody();
        } catch (Exception e) {
            log.error("Error while retrieving llm models", e);
            return new ArrayList<>();
        }
    }

    /**
     * Retrieves LLM models filtered by their type.
     *
     * @param type the type of LLM models to retrieve
     * @return List of {@link InferenceDetail} objects matching the specified type, or empty list if none found or in case of error
     */
    public List<InferenceDetail> getLlmModelsByType(String type) {
        log.debug("making call to prompt analyzer for loading embedding models");

        RestTemplate restTemplate = RestTemplateSingleton.getRestTemplate();
        // Create HTTP headers (you can add more headers like Authorization if needed)
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(org.springframework.http.MediaType.APPLICATION_JSON);

        if (isLocal) promptServiceUrl = LOCAL_URL;
        String url = String.format("%s/llm-model/getLlmModelsByType/type/%s", promptServiceUrl, type);
        log.debug("url to get llm models by type : {}", url);

        // Wrap the headers in an HttpEntity for GET request (optional)
        HttpEntity<Void> entity = new HttpEntity<>(headers);

        try {
            // Make the GET request to the audit endpoint
            ResponseEntity<List<InferenceDetail>> response = restTemplate.exchange(
                    url,
                    HttpMethod.GET,
                    entity,
                    new ParameterizedTypeReference<List<InferenceDetail>>() {
                    }
            );
            return response.getBody();
        } catch (Exception e) {
            log.error("Error while retrieving llm models", e);
            return new ArrayList<>();
        }
    }

    /**
     * Retrieves a specific knowledge base by its unique identifier.
     *
     * @param knowledgeBaseId the unique identifier of the knowledge base
     * @return {@link KnowledgeBase} object matching the ID, or null if not found or in case of error
     */
    public KnowledgeBase getKnowledgeBaseById(String knowledgeBaseId) {
        log.debug("making call to prompt analyzer for loading knowledge base by Primary key");
        RestTemplate restTemplate = RestTemplateSingleton.getRestTemplate();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(org.springframework.http.MediaType.APPLICATION_JSON);
        headers.set(SdkConstants.ACCESS_TOKEN_HEADER, CommonsAISaaSUtil.getAccessToken(httpServletRequest));

        if (isLocal) promptServiceUrl = LOCAL_URL;
        String url = String.format("%s/knowledge-base/v1/findById/%s", promptServiceUrl, knowledgeBaseId);
        log.debug("url to get knowledge-base : {}", url);

        HttpEntity<Void> entity = new HttpEntity<>(headers);

        try {
            // Make the GET request to the audit endpoint
            ResponseEntity<KnowledgeBase> response = restTemplate.exchange(
                    url,
                    HttpMethod.GET,
                    entity,
                    new ParameterizedTypeReference<KnowledgeBase>() {
                    }
            );
            return response.getBody();
        } catch (Exception e) {
            log.error("Error while retrieving knowledge base", e);
            return null;
        }
    }

}
