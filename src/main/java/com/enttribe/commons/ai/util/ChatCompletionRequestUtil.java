package com.enttribe.commons.ai.util;

import org.springframework.ai.openai.api.OpenAiApi.ChatCompletionRequest;

public class ChatCompletionRequestUtil {

    // Method to modify the model in a ChatCompletionRequest object
    public static ChatCompletionRequest withModel(ChatCompletionRequest originalRequest, String newModel) {
        return new ChatCompletionRequest(
                originalRequest.messages(),
                newModel, // Set the new model value
                originalRequest.store(),
                originalRequest.metadata(),
                originalRequest.frequencyPenalty(),
                originalRequest.logitBias(),
                originalRequest.logprobs(),
                originalRequest.topLogprobs(),
                originalRequest.maxTokens(),
                originalRequest.maxCompletionTokens(),
                originalRequest.n(),
                originalRequest.outputModalities(),
                originalRequest.audioParameters(),
                originalRequest.presencePenalty(),
                originalRequest.responseFormat(),
                originalRequest.seed(),
                originalRequest.serviceTier(),
                originalRequest.stop(),
                originalRequest.stream(),
                originalRequest.streamOptions(),
                originalRequest.temperature(),
                originalRequest.topP(),
                originalRequest.tools(),
                originalRequest.toolChoice(),
                originalRequest.parallelToolCalls(),
                originalRequest.user(),
                originalRequest.reasoningEffort(),
                originalRequest.webSearchOptions()
        );
    }
}
