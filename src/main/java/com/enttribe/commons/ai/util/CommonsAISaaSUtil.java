package com.enttribe.commons.ai.util;

import com.enttribe.commons.ai.constant.SdkConstants;
import jakarta.servlet.http.HttpServletRequest;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Base64;

public class CommonsAISaaSUtil {
    private static final Logger log = LoggerFactory.getLogger(CommonsAISaaSUtil.class);

    // Method to extract a claim from the JWT payload

    public static String getCustomerID(HttpServletRequest httpServletRequest) {
        try {
            String token = httpServletRequest.getHeader(SdkConstants.ACCESS_TOKEN_HEADER);
            if (token == null) {
                token = httpServletRequest.getHeader(SdkConstants.AUTHORIZATION_HEADER);
            }
            // Remove "Bearer " if present
            if (token.startsWith("Bearer ")) {
                token = token.substring(7);
            }

            // Split the token into three parts (header, payload, signature)
            String[] parts = token.split("\\.");

            // Check if the token has 3 parts (header, payload, signature)
            if (parts.length != 3) {
                throw new IllegalArgumentException("Invalid JWT token structure.");
            }

            // Decode the payload (Base64 URL encoded)
            String payload = parts[1];
            String decodedPayload = new String(Base64.getUrlDecoder().decode(payload));

            // Convert the decoded payload to a JSON object to extract claims
            JSONObject jsonPayload = new JSONObject(decodedPayload);

            // Return the claim value by the claim key
            return jsonPayload.optString("customerId", null); // Returns null if claim is not found
        } catch (Exception e) {
            log.error("error in extracting customer id from jwt token : {}", e.getMessage(), e);
            return SdkConstants.DEFAULT_CUSTOMER_ID;
        }
    }

    public static String getAccessToken(HttpServletRequest httpServletRequest) {
        try {
            String accessToken = httpServletRequest.getHeader(SdkConstants.ACCESS_TOKEN_HEADER);
            if (accessToken == null) {
                accessToken = httpServletRequest.getHeader(SdkConstants.AUTHORIZATION_HEADER);
            }
            return accessToken;
        } catch (Exception e) {
            log.error("error in extracting access token from request : {}", e.getMessage(), e);
            throw e;
        }
    }

}
