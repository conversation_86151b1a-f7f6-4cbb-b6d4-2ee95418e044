package com.enttribe.commons.ai.util;

import com.enttribe.commons.ai.config.AppProperties;
import com.enttribe.commons.ai.model.PromptModel;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Utility class providing audit-related functionality for the AI Commons SDK.
 * This class handles audit ID generation and chat options management for the application.
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public final class AuditUtils {

    private AuditUtils() {
        throw new UnsupportedOperationException("utility class can not be instantiated");
    }

    private static final String APPLICATION_NAME = AppProperties.getProperty("commons.ai.sdk.app.name");

    /**
     * Generates an audit ID by combining the application name with either a provided audit ID or a new UUID.
     *
     * @param auditId The existing audit ID to use, or null to generate a new UUID
     * @return A formatted string containing the application name and audit ID
     */
    public static String generateAuditId(String auditId, String applicationName) {
        String auditId1 = auditId == null ? UUID.randomUUID().toString() : auditId;
        if (applicationName == null) {
            applicationName = AuditUtils.APPLICATION_NAME;
        }
        return String.format("%s_%s", applicationName, auditId1);
    }

    /**
     * Generates a specific audit ID for JSON rectification operations by combining the application name,
     * a JSON parse identifier, and either a provided audit ID or a new UUID.
     *
     * @param auditId The existing audit ID to use, or null to generate a new UUID
     * @return A formatted string containing the application name, JSON parse identifier, and audit ID
     */
    public static String generateRectifyJsonAuditId(String auditId) {
        String auditId1 = auditId == null ? UUID.randomUUID().toString() : auditId;
        return String.format("%s_json_parse_%s", APPLICATION_NAME, auditId1);
    }

    /**
     * Generates a new random UUID string for audit purposes.
     *
     * @return A new random UUID as a string
     */
    public static String getAuditId() {
        return UUID.randomUUID().toString();
    }

    /**
     * Creates a map of chat options from a PromptModel object.
     * The map includes model configuration parameters such as the model name,
     * maximum tokens, temperature, and top P values.
     *
     * @param promptModel The PromptModel containing the chat configuration parameters
     * @return A Map containing the chat options
     */
    public static Map<String, Object> getChatOptionsMap(PromptModel promptModel) {
        Map<String, Object> chatOptionsMap = new HashMap<>();
        chatOptionsMap.put("model", promptModel.getModel());
        chatOptionsMap.put("maxTokens", promptModel.getMaxTokens());
        chatOptionsMap.put("temperature", promptModel.getTemperature());
        chatOptionsMap.put("topP", promptModel.getTopP());
        chatOptionsMap.put("reasoningEffort", promptModel.getReasoningEffort());

        return chatOptionsMap;
    }

}
