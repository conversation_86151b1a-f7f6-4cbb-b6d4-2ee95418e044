package com.enttribe.commons.ai.util;

import com.enttribe.commons.ai.model.rag.VectorMetaData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;

/**
 * Utility class for vector operations related to vector databases.
 * This class provides methods to validate vector database names and generate keys for Milvus vector stores.
 *
 * <AUTHOR>
 */
public final class VectorUtils {

    private static final List<String> SUPPORTED_VECTOR_DB_NAMES = List.of("redis");
    private static final Logger log = LoggerFactory.getLogger(VectorUtils.class);

    private VectorUtils() {
        throw new UnsupportedOperationException("utility class can not be instantiated");
    }

    /**
     * Validates if the provided vector database name is supported.
     *
     * @param vectorDatabase the name of the vector database to validate
     * @throws IllegalArgumentException if the vector database name is not supported
     */
    public static void validateVectorDatabaseName(String vectorDatabase) {
        boolean contains = SUPPORTED_VECTOR_DB_NAMES.contains(vectorDatabase);
        if (!contains) throw new IllegalArgumentException("unsupported vector database name: " + vectorDatabase);
    }

    /**
     * Generates a key for the Milvus vector store using the provided metadata.
     *
     * @param metaData metadata containing vector database, database name, and collection name
     * @return a string key for the Milvus vector store
     */
    public static String generateVectorStoreKey(VectorMetaData metaData) {
        if (metaData.getVectorDatabase().equals("redis")) {
            return String.format("%s_%s_%s_%s", metaData.getCustomerId(), metaData.getVectorDatabase(), metaData.getIndexName(), metaData.getPrefix());
        } else {
            throw new IllegalStateException("unsupported database");
        }
    }

    /**
     * Generates a key for the Milvus vector store using the provided metadata.
     *
     * @param vectorMetaData metadata containing vector database, database name, and collection name
     * @return a string key for the vector store
     */
    public static String generateVectorStoreKey(Map<String, Object> vectorMetaData) {
        String customerId = (String) vectorMetaData.get("customerId");
        String vectorDatabase = (String) vectorMetaData.get("vectorDatabase");
        VectorUtils.validateVectorDatabaseName(vectorDatabase);

        String secondPart;
        String thirdPart;
        if (vectorDatabase.equals("redis")) {
            secondPart = (String) vectorMetaData.get("indexName");
            thirdPart = (String) vectorMetaData.get("prefix");
        } else {
            throw new IllegalStateException("unsupported database");
        }
        String vectorStoreKey = String.format("%s_%s_%s_%s", customerId, vectorDatabase, secondPart, thirdPart);
        log.debug("vectorStoreKey is : {}", vectorStoreKey);
        return vectorStoreKey;
    }

}
