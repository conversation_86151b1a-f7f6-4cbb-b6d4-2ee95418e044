package com.enttribe.commons.ai.model;

import com.fasterxml.jackson.annotation.JsonAnySetter;

import java.util.List;
import java.util.Map;
import java.util.HashMap;

public class ModelProviderConfig {

    // Map to hold the dynamic provider names and their respective providers
    private Map<String, Provider> providers = new HashMap<>();

    @JsonAnySetter
    public void setProvider(String providerName, Provider provider) {
        this.providers.put(providerName, provider);
    }

    public Map<String, Provider> getProviders() {
        return providers;
    }

    public void setProviders(Map<String, Provider> providers) {
        this.providers = providers;
    }

    // Method to get the Provider by provider name
    public Provider getProviderByName(String providerName) {
        if (providers == null) {
            return null;
        }
        return this.providers.get(providerName);
    }

    public static class Provider {
        private List<Model> models;

        public static Fallback getFallbackForModel(String modelName, Provider provider) {

            for (Model model : provider.getModels()) {
                if (model.getModel().equals(modelName)) {
                    return model.getFallback();
                }
            }

            return null; // Return null if no matching model is found
        }

        public List<Model> getModels() {
            return models;
        }

        public void setModels(List<Model> models) {
            this.models = models;
        }
    }

    public static class Model {
        private String model;
        private Fallback fallback;

        public String getModel() {
            return model;
        }

        public void setModel(String model) {
            this.model = model;
        }

        public Fallback getFallback() {
            return fallback;
        }

        public void setFallback(Fallback fallback) {
            this.fallback = fallback;
        }
    }

    public static class Fallback {
        private String provider;
        private String model;

        public String getProvider() {
            return provider;
        }

        public void setProvider(String provider) {
            this.provider = provider;
        }

        public String getModel() {
            return model;
        }

        public void setModel(String model) {
            this.model = model;
        }

        @Override
        public String toString() {
            return "Fallback{" +
                    "provider='" + provider + '\'' +
                    ", model='" + model + '\'' +
                    '}';
        }
    }
}
