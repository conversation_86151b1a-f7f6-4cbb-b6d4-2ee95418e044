//package com.enttribe.commons.ai.test;
//
//import jakarta.websocket.HandshakeResponse;
//import jakarta.websocket.server.HandshakeRequest;
//import org.springframework.web.socket.server.support.HttpSessionHandshakeInterceptor;
//import org.springframework.web.socket.WebSocketHandler;
//
//import java.util.Map;
//
//public class WebSocketHandshakeInterceptor extends HttpSessionHandshakeInterceptor {
//
//    @Override
//    public boolean beforeHandshake(HandshakeRequest request, HandshakeResponse response, WebSocketHandler wsHandler, Map<String, Object> attributes) throws Exception {
//        // You can extract authentication or session details here
////        String accessToken = request.getHeaders().getFirst("Authorization");
////        System.out.println("Access Token: " + accessToken);
//
//        // Continue with the handshake
//        return super.beforeHandshake(request, response, wsHandler, attributes);
//    }
//}