package com.enttribe.commons.ai.test;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;

@Configuration
@EnableWebSocket
public class WebSocketConfig implements WebSocketConfigurer {

    @Autowired
    private MyWebSocketHandler myWebSocketHandler;

    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        registry.addHandler(myWebSocketHandler(), "/web")
//                .addInterceptors(new WebSocketHandshakeInterceptor())
                .setAllowedOrigins("*"); // Configure allowed origins here
    }

    public MyWebSocketHandler myWebSocketHandler() {
        return myWebSocketHandler;
    }
}
