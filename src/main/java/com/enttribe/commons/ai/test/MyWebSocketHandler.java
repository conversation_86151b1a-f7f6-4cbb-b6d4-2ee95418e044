package com.enttribe.commons.ai.test;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import java.util.Map;

@Component
public class MyWebSocketHandler extends TextWebSocketHandler {

    @Autowired
    private Test test;

    @Override
    public void handleTextMessage(WebSocketSession session, TextMessage message) throws Exception {
        // Process the received message (can be anything from a user)
        String payload = message.getPayload();
        System.out.println("Received message: " + payload);

        test.test(Map.of());

        // Send a response back to the client
        session.sendMessage(new TextMessage("Hello, you sent: " + payload));
    }
}